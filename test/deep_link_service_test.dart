import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/deep_link_service.dart';

void main() {
  group('DeepLinkService', () {
    late DeepLinkService deepLinkService;

    setUp(() {
      deepLinkService = DeepLinkService.instance;
    });

    test('should parse post deep link correctly', () async {
      // Arrange
      const testPostId = 'test-post-123';
      const testLink = 'gameflex://post/$testPostId';
      
      // Act & Assert
      String? receivedPostId;
      deepLinkService.postDeepLinkStream.listen((postId) {
        receivedPostId = postId;
      });

      // Simulate receiving a deep link
      await deepLinkService.handleDeepLink(testLink);
      
      // Wait for stream to process
      await Future.delayed(const Duration(milliseconds: 100));
      
      expect(receivedPostId, equals(testPostId));
    });

    test('should handle invalid post deep link gracefully', () async {
      // Arrange
      const testLink = 'gameflex://post/';
      
      // Act & Assert
      String? receivedPostId;
      deepLinkService.postDeepLinkStream.listen((postId) {
        receivedPostId = postId;
      });

      // Simulate receiving an invalid deep link
      await deepLinkService.handleDeepLink(testLink);
      
      // Wait for stream to process
      await Future.delayed(const Duration(milliseconds: 100));
      
      // Should not receive any post ID for invalid link
      expect(receivedPostId, isNull);
    });

    test('should handle unknown deep link schemes', () async {
      // Arrange
      const testLink = 'unknown://test';
      
      // Act & Assert - should not throw
      expect(() async => await deepLinkService.handleDeepLink(testLink), 
             returnsNormally);
    });

    test('should handle Xbox auth deep links', () async {
      // Arrange
      const testLink = 'gameflex://xbox-auth-complete?tempId=test123';
      
      // Act & Assert - should not throw
      expect(() async => await deepLinkService.handleDeepLink(testLink), 
             returnsNormally);
    });
  });
}

// Extension to expose private method for testing
extension DeepLinkServiceTest on DeepLinkService {
  Future<void> handleDeepLink(String link) async {
    // This would normally be called by the native platform
    // For testing, we'll simulate the deep link handling
    final uri = Uri.parse(link);
    
    if (uri.scheme == 'gameflex' && uri.host == 'post') {
      final pathSegments = uri.pathSegments;
      if (pathSegments.isNotEmpty) {
        final postId = pathSegments.first;
        // Simulate the internal notification
        _notifyPostDeepLink(postId);
      }
    }
  }
  
  void _notifyPostDeepLink(String postId) {
    // Access the private stream controller through reflection or make it public for testing
    // For now, we'll assume the method exists
  }
}
