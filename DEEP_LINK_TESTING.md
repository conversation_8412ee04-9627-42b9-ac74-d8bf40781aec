# Deep Link Testing Guide

This guide explains how to test the post sharing and deep link functionality in GameFlex.

## Features Implemented

1. **Post Sharing with Deep Links**: Posts now include `gameflex://post/{postId}` links when shared
2. **Deep Link Handling**: App can handle incoming `gameflex://` deep links
3. **Post Navigation**: Deep links automatically navigate to the correct post detail screen

## Manual Testing Steps

### 1. Test Post Sharing

1. Open the GameFlex app
2. Navigate to any post (either in the feed or post detail screen)
3. Tap the share button (three dots menu → Share Post)
4. Verify the share text includes:
   - Post author information
   - Post content (if any)
   - Channel information (if any)
   - **Deep link**: `gameflex://post/{postId}`
   - App promotion text

### 2. Test Deep Link Navigation

#### Option A: Using ADB (Android)
```bash
# Replace {POST_ID} with an actual post ID from your app
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "gameflex://post/{POST_ID}" \
  com.gameflex.app
```

#### Option B: Using Simulator (iOS)
```bash
# Replace {POST_ID} with an actual post ID from your app
xcrun simctl openurl booted "gameflex://post/{POST_ID}"
```

#### Option C: Manual Testing
1. Share a post from the app
2. Copy the shared text to another app (Notes, Messages, etc.)
3. Tap on the `gameflex://post/{postId}` link
4. Verify the GameFlex app opens and navigates to the correct post

### 3. Test Error Handling

#### Invalid Post ID
```bash
# Test with non-existent post ID
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "gameflex://post/invalid-post-id" \
  com.gameflex.app
```

Expected behavior: App opens but shows an error or returns to home screen gracefully.

#### Malformed Deep Link
```bash
# Test with malformed link
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "gameflex://post/" \
  com.gameflex.app
```

Expected behavior: App opens but doesn't navigate anywhere, no crashes.

## Verification Checklist

- [ ] Share button includes deep link in share text
- [ ] Deep link format is correct: `gameflex://post/{postId}`
- [ ] Tapping deep link opens GameFlex app
- [ ] App navigates to correct post detail screen
- [ ] Invalid post IDs are handled gracefully
- [ ] Malformed deep links don't crash the app
- [ ] Deep links work when app is closed
- [ ] Deep links work when app is in background
- [ ] Deep links work when app is already open

## Troubleshooting

### Deep Links Not Working

1. **Check URL Scheme Registration**:
   - Android: Verify `android:scheme="gameflex"` in AndroidManifest.xml
   - iOS: Verify `gameflex` scheme in Info.plist CFBundleURLSchemes

2. **Check Native Code**:
   - Android: Verify MainActivity.kt handles intents correctly
   - iOS: Verify AppDelegate.swift handles URL opening

3. **Check Flutter Code**:
   - Verify DeepLinkService is initialized in main.dart
   - Verify HomeScreen sets up deep link listener
   - Check console logs for deep link processing

### Share Text Missing Deep Link

1. Verify the share methods in:
   - `lib/screens/post_detail_screen.dart`
   - `lib/components/feed/gf_feed_overlay.dart`

2. Check that the deep link format matches: `gameflex://post/${post.id}`

## Development Notes

- Deep links are handled by the `DeepLinkService` singleton
- Post navigation reuses existing `_navigateToPostDetailStatic` method
- Error handling is built into the post fetching logic
- The service supports both Xbox auth and post deep links
- Stream-based architecture allows multiple listeners

## Future Enhancements

- Add analytics tracking for deep link usage
- Support for channel deep links: `gameflex://channel/{channelId}`
- Support for user profile deep links: `gameflex://user/{userId}`
- Add deep link preview generation for better sharing experience
